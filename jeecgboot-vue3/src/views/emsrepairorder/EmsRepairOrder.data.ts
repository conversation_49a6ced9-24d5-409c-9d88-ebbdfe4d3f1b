import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '故障标题',
    align:"center",
    dataIndex: 'faultTitle',
    width: 150
   },
   {
    title: '关联设备',
    align:"center",
    dataIndex: 'equipmentId',
    width: 120
   },
   {
    title: '发起人',
    align:"center",
    dataIndex: 'reportId_dictText',
    width: 100
   },
   {
    title: '负责人',
    align:"center",
    dataIndex: 'principalId_dictText',
    width: 100
   },
   {
    title: '故障描述',
    align:"center",
    dataIndex: 'faultDescription',
    width: 200,
    ellipsis: true
   },
   {
    title: '维修部门',
    align:"center",
    dataIndex: 'repairDeptName',
    width: 120
   },
   {
    title: '故障图片',
    align:"center",
    dataIndex: 'attachment',
    customRender:render.renderImage,
    width: 100
   },
   {
    title: '故障附件',
    align:"center",
    dataIndex: 'faultAttachment',
    width: 100
   },
   {
    title: '处理图片',
    align:"center",
    dataIndex: 'handleImages',
    customRender:render.renderImage,
    width: 100
   },
   {
    title: '处理附件',
    align:"center",
    dataIndex: 'handleAttachment',
    width: 100
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '故障标题',
    field: 'faultTitle',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '关联设备',
    field: 'equipmentId',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '维修部门',
    field: 'repairDeptName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '发起人',
    field: 'reportId',
    component: 'JSelectUser',
    colProps: { span: 6 },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '故障标题',
    field: 'faultTitle',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入故障标题!'},
          ];
     },
  },
  {
    label: '关联设备表',
    field: 'equipmentId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入关联设备表!'},
          ];
     },
  },
  {
    label: '发起人',
    field: 'reportId',
    component: 'JSelectUser',
    componentProps:{
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择发起人!'},
          ];
     },
  },
  {
    label: '故障描述',
    field: 'faultDescription',
    component: 'InputTextArea',
    componentProps:{
      rows: 4,
      placeholder: '请详细描述故障现象、发生时间等信息'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入故障描述!'},
          ];
     },
  },
  {
    label: '故障图片',
    field: 'attachment',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 5,
        text: '上传故障现场图片'
      },
  },
  {
    label: '故障附件',
    field: 'faultAttachment',
    component: 'JUpload',
    componentProps:{
      text: '上传相关文档或其他附件'
     },
  },
  {
    label: '维修审批流程',
    field: 'repairDeptRole',
    component: 'JDeptRoleSelector',
    componentProps:{
      placeholder: '请选择维修部门和审批角色'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择维修部门和审批角色!'},
          ];
     },
  },
  {
    label: '负责人',
    field: 'principalId',
    component: 'JSelectUser',
    componentProps:{
    },
    ifShow: false, // 新增时隐藏，编辑时显示
  },
  {
    label: '处理图片',
    field: 'handleImages',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 5,
        text: '上传处理过程图片'
      },
    ifShow: false, // 新增时隐藏，编辑时显示
  },
  {
    label: '处理附件',
    field: 'handleAttachment',
    component: 'JUpload',
    componentProps:{
      text: '上传处理相关文档'
     },
    ifShow: false, // 新增时隐藏，编辑时显示
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
	// 隐藏字段：维修部门ID
	{
	  label: '',
	  field: 'repairDeptId',
	  component: 'Input',
	  show: false
	},
	// 隐藏字段：维修部门名称
	{
	  label: '',
	  field: 'repairDeptName',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  faultTitle: {title: '故障标题',order: 0,view: 'text', type: 'string',},
  equipmentId: {title: '关联设备',order: 1,view: 'text', type: 'string',},
  reportId: {title: '发起人',order: 2,view: 'sel_user', type: 'string',},
  principalId: {title: '负责人',order: 3,view: 'sel_user', type: 'string',},
  faultDescription: {title: '故障描述',order: 4,view: 'textarea', type: 'string',},
  repairDeptName: {title: '维修部门',order: 5,view: 'text', type: 'string',},
  attachment: {title: '故障图片',order: 6,view: 'image', type: 'string',},
  faultAttachment: {title: '故障附件',order: 7,view: 'file', type: 'string',},
  handleImages: {title: '处理图片',order: 8,view: 'image', type: 'string',},
  handleAttachment: {title: '处理附件',order: 9,view: 'file', type: 'string',},
};

/**
* 获取新增表单配置（隐藏处理相关字段和负责人字段）
*/
export function getAddFormSchema(): FormSchema[] {
  return formSchema.map(item => {
    // 新增时隐藏负责人、处理图片、处理附件字段
    if (['principalId', 'handleImages', 'handleAttachment'].includes(item.field)) {
      return {
        ...item,
        ifShow: false
      };
    }
    return item;
  });
}

/**
* 获取编辑表单配置（显示所有字段）
*/
export function getEditFormSchema(): FormSchema[] {
  return formSchema.map(item => {
    return {
      ...item,
      ifShow: true
    };
  });
}

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
