package org.jeecg.modules.demo.emsrepairorderapprovaltemplates.service.impl;

import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.entity.EmsRepairOrderApprovalTemplates;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.mapper.EmsRepairOrderApprovalTemplatesMapper;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.service.IEmsRepairOrderApprovalTemplatesService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 审批模板表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
@Service
@Slf4j
public class EmsRepairOrderApprovalTemplatesServiceImpl extends ServiceImpl<EmsRepairOrderApprovalTemplatesMapper, EmsRepairOrderApprovalTemplates> implements IEmsRepairOrderApprovalTemplatesService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean activateTemplate(String templateId, String sysOrgCode) {
        try {
            log.info("开始激活模板，templateId: {}, sysOrgCode: {}", templateId, sysOrgCode);

            // 1. 先将该部门的所有模板设置为未激活状态
            UpdateWrapper<EmsRepairOrderApprovalTemplates> deactivateWrapper = new UpdateWrapper<>();
            deactivateWrapper.lambda()
                    .eq(EmsRepairOrderApprovalTemplates::getSysOrgCode, sysOrgCode)
                    .set(EmsRepairOrderApprovalTemplates::getIsActive, "0");

            boolean deactivateResult = this.update(deactivateWrapper);
            log.info("部门 {} 所有模板设置为未激活状态，结果: {}", sysOrgCode, deactivateResult);

            // 2. 激活指定的模板
            UpdateWrapper<EmsRepairOrderApprovalTemplates> activateWrapper = new UpdateWrapper<>();
            activateWrapper.lambda()
                    .eq(EmsRepairOrderApprovalTemplates::getId, templateId)
                    .eq(EmsRepairOrderApprovalTemplates::getSysOrgCode, sysOrgCode)
                    .set(EmsRepairOrderApprovalTemplates::getIsActive, "1");

            boolean activateResult = this.update(activateWrapper);
            log.info("激活模板 {} 结果: {}", templateId, activateResult);

            return activateResult;
        } catch (Exception e) {
            log.error("激活模板失败，templateId: {}, sysOrgCode: {}", templateId, sysOrgCode, e);
            throw new RuntimeException("激活模板失败: " + e.getMessage());
        }
    }

}
